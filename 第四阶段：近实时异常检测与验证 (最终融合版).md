---

### **第四阶段：近实时异常检测与验证 (最终融合版)**

**核心目标：** 部署一个自动化的工作流，该流程能够处理新获取的卫星数据，识别火灾异常，并最终以与论文一致的标准和形式，对检测结果进行严格的验证和可视化。

#### **所需组件清单 (精确定义)**

1.  **实时GOES-17数据源 (`real_time_goes_feed`)**: `GOES/ABI-L2-CMIPC` 产品的最新影像。
2.  **官方基准验证数据 (`ground_truth_data`)**: **GOES-17 ABI L2+ Fire/Hotspot Characterization (FHC)** 产品。
3.  **MODIS土地覆盖地图 (`modis_lc`)**: 第一阶段准备的静态`MCD12Q1`地表类型图。
4.  **跨尺度转换模型 (`M`)**: 已训练好的模型文件，如 `scale_converter.pth`。
5.  **分类别的ConvLSTM预测模型集**: 一组按地表类型ID命名的模型文件，如 `P_1.pth`, `P_12.pth` 等。

---

### **Part A: 实时检测工作流 (Online Processing)**

*   **执行环境：** 一个在服务器上持续运行的脚本。
*   **核心逻辑：** 该脚本会定期（例如每10分钟）被唤醒，执行一次火灾检测，并将结果记录下来。

**详细步骤：**

1.  **数据获取与准备 (t_k 时刻):**
    a.  获取最新的真实GOES-17影像 $R_{GOES}(\lambda, t_k)$。
    b.  获取该影像之前12小时（72个时间点）的真实GOES历史影像序列 `history_sequence`。

2.  **逐像素生成“理论预测”影像 (`\hat{R}'_{GOES}`):**
    a.  初始化一个与GOES影像同尺寸的全零数组 `predicted_image`。
    b.  遍历 `predicted_image` 的每一个像素 `(x, y)`:
        i.   查询 `modis_lc` 地图，获取该像素对应的地表类型ID。
        ii.  根据ID加载专属的ConvLSTM预测模型 `P_id.pth`。
        iii. 从 `history_sequence` 中提取该像素的历史值，并输入到加载的模型中，得到一个预测值。
        iv.  将此预测值填充到 `predicted_image` 的 `(x, y)` 位置。

3.  **残差计算与火点识别:**
    a.  计算残差影像：`residual_image = |R_GOES - predicted_image|`。
    b.  应用阈值：`fire_mask = (residual_image > 1500)`。
    c.  提取 `fire_mask` 中所有值为`True`的像素坐标。

4.  **结果记录:**
    a.  将提取出的火点像素坐标转换为地理经纬度。
    b.  将当前时间戳 $t_k$ 和所有检测到的火点经纬度，作为一条新记录，追加到一个**日志文件**或**数据库**中（例如 `my_detection_log.csv`）。

---

### **Part B: 结果验证与分析 (Offline Analysis)**

*   **执行环境：** 这是一个独立的分析脚本，在你收集了一段时间（例如一个完整的火灾周期）的检测日志后执行。
*   **核心逻辑：** 将我们的检测日志与官方FHC基准数据进行严格的定量与定性比较。

**详细步骤：**

1.  **准备验证数据:**
    a.  从NOAA CLASS等官方渠道，下载与你研究时段和区域完全对应的 **GOES FHC产品**。
    b.  读取FHC产品文件，将其中的火点记录（时间、经纬度、置信度等）整理成一个标准格式的数据表。
    c.  加载你在**Part A**中生成的检测日志 `my_detection_log.csv`。

2.  **定义时空匹配规则:**
    *   **空间窗口:** 定义一个半径为 **2-3公里** 的圆形缓冲区。
    *   **时间窗口:** 定义一个非对称的时间窗口，例如 `[T_FHC - 40 minutes, T_FHC + 20 minutes]`，以验证提前预警能力。

3.  **执行匹配与分类:**
    a.  初始化计数器: `True_Positives = 0`, `False_Positives = 0`, `False_Negatives = 0`, 以及一个空列表 `latency_list`。
    b.  **遍历我方检测日志:** `For` 每一个 `my_fire` in `my_detection_log`:
        *   在FHC数据中搜索是否存在一个时空上匹配的 `fhc_fire`。
        *   **If** 匹配: `True_Positives += 1`，并计算延迟 `latency = my_fire.time - fhc_fire.time`，然后 `latency_list.append(latency)`。
        *   **Else**: `False_Positives += 1`。
    c.  **遍历FHC官方数据:** `For` 每一个 `fhc_fire`:
        *   反向搜索我方日志，看是否存在匹配。
        *   **If** 不存在匹配: `False_Negatives += 1`。

4.  **计算最终评估指标:**
    a.  **检测率 (POD):**
        $$ \text{POD} = \frac{\text{True Positives}}{\text{True Positives} + \text{False Negatives}} $$
    b.  **虚警率 (FAR):**
        $$ \text{FAR} = \frac{\text{False Positives}}{\text{True Positives} + \text{False Positives}} $$
    c.  **平均检测延迟 (Average Latency):**
        $$ \text{Average Latency} = \text{mean}(\text{latency\_list}) $$
        *(将结果从秒或毫秒转换为分钟，预期得到一个接近-20的负值)*

---

### **Part C: 可视化成果生成 (Final Presentation)**

*   **执行环境：** 使用QGIS或Python地理可视化库，对**Part B**的匹配结果进行可视化。
*   **核心逻辑：** 创建与论文中风格和内容一致的图表。

**详细步骤：**

1.  **生成空间分布对比图 (复现 Fig. 6):**
    a.  加载研究区的卫星影像作为底图。
    b.  将 `my_detection_log.csv` 中的所有火点，以**红色圆圈**的形式绘制在地图上。
    c.  将FHC官方数据中的所有火点，以**蓝色三角**的形式绘制在同一张地图上。
    d.  添加清晰的图例，明确标注两种符号的含义。
    e.  输出为高分辨率图片。

2.  **生成时间序列对比图 (复现 Fig. 7):**
    a.  按固定时间间隔（例如每小时）对 `my_detection_log` 进行分组，并统计每个间隔内的火点数量，形成时间序列A。
    b.  同样地，对FHC官方数据进行分组统计，形成时间序列B。
    c.  在同一个图表中，以时间为X轴，火点数量为Y轴，绘制两条折线图，分别代表时间序列A和B。
    d.  使用不同的颜色和线型区分两条线，并添加图例。
    e.  输出为高分辨率图片。

**最终产出:**
至此，第四阶段全部完成。你的最终成果将包括：一个自动记录火灾的日志文件，一份包含POD、FAR和平均延迟的定量评估报告，以及两张与论文风格一致、能够直观展示你复现成果性能的高质量图表。