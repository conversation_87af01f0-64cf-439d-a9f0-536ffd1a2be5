---

### **第一阶段：数据获取 (精确复现版)**

在开始之前，我们需要一个统一的“行动区域”，这个区域将应用于所有后续的数据筛选。

#### **核心参数：研究区域 (Area of Interest - AOI)**

这是所有数据筛选的第一步，也是最重要的一步。我们必须确保所有数据都从同一个地理范围内提取。

*   **数据来源：** 论文 **Table I**，"Kernel coefficient of the BRDF" 和 "Land Cover" 行中的 "Spatial Range" 列。
*   **具体数值：**
    *   纬度 (Latitude): `45.24°N` 到 `49.77°N`
    *   经度 (Longitude): `118.27°W` 到 `124.20°W`
*   **方法：**
    1.  在你的脚本中，创建一个地理几何对象（`ee.Geometry.Rectangle`）。
    2.  这个矩形的坐标点必须严格按照以下顺序和数值设定：`[min_longitude, min_latitude, max_longitude, max_latitude]`。
    3.  根据论文数值，对应的坐标为：`[-124.20, 45.24, -118.27, 49.77]`。
    4.  将这个几何对象命名为 `aoi`。在后续的所有数据筛选中，你都将使用这个`aoi`作为地理过滤器。

---

现在，我们来逐一获取论文中提到的三种数据。

### **数据一：GOES-17 卫星影像 (实时观测员)**

*   **数据来源：** 论文 **Table I**，"GOES-17" 列。
*   **GEE 产品名称：** `GOES/ABI-L2-CMIPC` (这对应了论文中提到的L2级数据)。
*   **角色：** 提供高频率的真实世界观测数据，用于在最后阶段与模型预测进行对比。

**筛选方法：**

1.  **选择数据集：** 在你的脚本中，加载`ee.ImageCollection('GOES/ABI-L2-CMIPC')`。
2.  **应用时间过滤器：**
    *   **来源：** Table I, "Time Range" 行。
    *   **数值：** `2021.07.12` 到 `2021.07.18`。
    *   **操作：** 对影像集应用一个日期过滤器，起始时间为`2021-07-12T00:00:00`，结束时间为`2021-07-19T00:00:00` (使用第二天零点是为了包含18号全天的数据)。
3.  **应用地理过滤器：**
    *   **来源：** 我们在上面定义的核心参数。
    *   **操作：** 对影像集应用一个地理边界过滤器，使用你之前创建的`aoi`对象。

**阶段性成果：** 你在脚本中获得了一个名为`goes_collection`的逻辑对象。它是一个“指针”，指向了GEE数据库中所有满足上述时空条件的GOES-17影像，但并未实际下载它们。

---

### **数据二：MODIS 土地覆盖产品 (地表蓝图)**

*   **数据来源：** 论文 **Table I**，"Land Cover" 列，及正文 **Section III-A** 中明确提到的 "MODIS LC Type Yearly Global 500 m"。
*   **GEE 产品名称：** `MODIS/006/MCD12Q1` (这是标准的MODIS全球土地覆盖年度产品)。
*   **角色：** 提供高分辨率的地表类型基础地图，是物理模型计算的基础。

**筛选方法：**

1.  **选择数据集：** 加载`ee.ImageCollection('MODIS/006/MCD12Q1')`。
2.  **应用时间过滤器 (特别注意)：**
    *   **来源：** 论文正文提到这是“年度”产品。Table I中的日期范围(`2021.07.12` ~ `2021.07.18`)是为了与其他数据对齐，但实际获取时应按年份筛选。
    *   **操作：** 对影像集应用日期过滤器，筛选**2021年**的全年数据（例如，从`2021-01-01`到`2021-12-31`）。由于这是年度产品，这个范围内只会有一张影像。使用`.first()`方法来获取这唯一的一张影像。
3.  **选择波段：**
    *   **来源：** `MCD12Q1`产品包含多种土地覆盖分类系统。最常用且最标准的是IGBP分类系统。
    *   **操作：** 从获取的影像中，明确选择名为`'LC_Type1'`的波段。
4.  **应用地理过滤器：**
    *   **操作：** 虽然在后续计算中才会用到具体区域，但最佳实践是在逻辑上已经明确这是针对`aoi`的。

**阶段性成果：** 你获得了一个名为`modis_lc`的逻辑对象。它指向2021年度覆盖全球的土地覆盖地图中的`LC_Type1`波段。

---

### **数据三：MODIS BRDF 模型系数 (物理模型参数)**

*   **数据来源：** 论文 **Table I**，"Kernel coefficient of the BRDF" 列。
*   **GEE 产品名称：** `MODIS/006/MCD43A1` (该产品提供了每日的BRDF/Albedo模型参数，分辨率为500米，与论文描述完全一致)。
*   **角色：** 为物理模型提供最核心的输入参数，即地表对光的反射特性 ($f_{iso}, f_{vol}, f_{geo}$)。

**筛选方法：**

1.  **选择数据集：** 加载`ee.ImageCollection('MODIS/006/MCD43A1')`。
2.  **应用时间过滤器：**
    *   **来源：** Table I, "Time Range" 行。
    *   **数值：** `2021.07.12` 到 `2021.07.18`。
    *   **操作：** 应用日期过滤器，起始时间`2021-07-12`，结束时间`2021-07-19`。
3.  **应用地理过滤器：**
    *   **操作：** 使用`aoi`对象进行地理过滤。
4.  **选择波段 (关键步骤)：**
    *   **来源：** 论文公式(1)需要$f_{iso}, f_{vol}, f_{geo}$这三个参数。`MCD43A1`产品为每个MODIS波段（例如红光、蓝光等）都提供了这三个参数。
    *   **操作：** 你需要选择所有与BRDF模型参数相关的波段。这些波段在GEE中通常命名为`'BRDF_Albedo_Parameters_Band*'` (例如 `'BRDF_Albedo_Parameters_Band1'`, `'BRDF_Albedo_Parameters_Band2'` 等)。明确地将这些参数波段全部选中。

**阶段性成果：** 你获得了一个名为`modis_brdf_collection`的逻辑对象。它指向在指定时空范围内，所有包含BRDF物理参数的MODIS每日数据。

---

### **第一阶段完成清单**

至此，你已严格按照论文的描述，完成了所有数据的定位和逻辑筛选。在你的脚本环境中，你应该拥有以下三个核心的、已准备就绪的逻辑对象：

1.  **`goes_collection`**: 一个包含多张影像的集合，代表了研究时段内真实、低分辨率的地球观测。
2.  **`modis_lc`**: 一张单一的影像，代表了2021年研究区高分辨率的地表类型分布。
3.  **`modis_brdf_collection`**: 一个包含多张影像的集合，代表了研究时段内每日、高分辨率的地表光学特性参数。
