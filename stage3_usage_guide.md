# 第三阶段：深度学习模型训练使用指南

## 🎯 概述

第三阶段实现了完整的深度学习模型训练流程，严格按照文档要求构建了两个核心模型：
1. **跨尺度转换模型 (M)**：将高分辨率理想影像映射为低分辨率真实影像风格
2. **ConvLSTM时间序列预测模型 (P)**：针对每种地表类型的时间序列预测

## 📋 环境要求

### 硬件要求
- **推荐**：NVIDIA GPU (8GB+ 显存)
- **最低**：CPU (训练速度较慢)
- **内存**：16GB+ RAM

### 软件环境
```bash
# 激活DL环境
conda activate DL

# 确保安装必要的包
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install scikit-learn
pip install earthengine-api
```

## 🚀 使用方法

### 1. 基本运行
```bash
# 在DL环境中运行
conda activate DL
python stage3_deep_learning.py
```

### 2. 检查环境
程序会自动检查：
- ✅ Google Earth Engine连接
- ✅ PyTorch版本
- ✅ CUDA可用性
- ✅ 前两阶段数据

### 3. 训练配置

#### 跨尺度转换模型配置
```python
'cross_scale_model': {
    'hidden_size': 128,
    'learning_rate': 0.001,
    'batch_size': 32,
    'epochs': 50,
    'loss_function': 'mse'
}
```

#### ConvLSTM模型配置（严格按文档要求）
```python
'convlstm_model': {
    'input_dim': 1,
    'hidden_dim': 32,        # 每层32个隐藏单元
    'num_layers': 2,         # 2层
    'learning_rate': 0.0002, # 严格设置为0.0002
    'batch_size': 8,         # 低配策略：从8开始
    'epochs': 20,            # 低配策略：从20轮开始
    'sequence_length': 72,   # 12小时 * 6次/小时 = 72
    'loss_function': 'mse'
}
```

## 📊 训练流程

### Part A: 跨尺度转换模型
1. **样本制备**：筛选12000组无云样本对
2. **数据划分**：7:3比例分为训练集和验证集
3. **模型训练**：使用MLP架构，MSE损失函数

### Part B: ConvLSTM时间序列预测模型
1. **生成理想低分影像集**：通过模型M转换1008张虚拟影像
2. **按地表类型筛选**：每种类型提取3500条时间序列
3. **制作样本对**：72个时间点预测下一个时间点
4. **模型训练**：针对每种地表类型训练独立的ConvLSTM

## ⚙️ 性能优化

### 低配置策略
如果遇到显存不足 (CUDA out of memory)：

1. **降低批大小**
```python
# 在代码中修改
'batch_size': 4  # 从8降到4，甚至2
```

2. **减少训练轮数**
```python
# 先用少量轮数验证流程
'epochs': 10  # 从20降到10
```

3. **使用CPU训练**
```python
# 程序会自动检测，如果无GPU则使用CPU
device = torch.device('cpu')
```

### 高配置优化
如果有充足的GPU资源：

1. **增加批大小**
```python
'batch_size': 16  # 或32
```

2. **增加训练轮数**
```python
'epochs': 100  # 或更多
```

## 📁 输出结果

### 文件结构
```
stage3_results_YYYYMMDD_HHMMSS/
├── training_config.json      # 训练配置
├── model_info.json          # 模型信息
├── cross_scale_model_best.pth    # 跨尺度转换模型
├── convlstm_model_1_best.pth     # 地表类型1的ConvLSTM模型
├── convlstm_model_2_best.pth     # 地表类型2的ConvLSTM模型
└── ...
```

### 模型文件说明
- **cross_scale_model_best.pth**：跨尺度转换模型权重
- **convlstm_model_{type}_best.pth**：各地表类型的ConvLSTM模型权重

## 🔍 监控训练

### 训练日志
程序会实时显示：
- 每个epoch的训练损失和验证损失
- 最佳模型保存信息
- 各地表类型的训练进度

### 示例输出
```
Epoch [5/20], Train Loss: 0.001234, Val Loss: 0.001456
✓ 地表类型 1 模型训练完成，最佳验证损失: 0.001234
```

## ⚠️ 注意事项

### 1. 数据依赖
- 确保第一阶段和第二阶段已完成
- 虚拟影像集Asset必须可访问
- GOES-17和MODIS数据正常加载

### 2. 内存管理
- 大批量训练可能消耗大量内存
- 建议监控系统资源使用情况
- 必要时调整批大小和工作进程数

### 3. 训练时间
- CPU训练：数小时到数天
- GPU训练：数十分钟到数小时
- 具体时间取决于硬件配置和数据量

### 4. 模型保存
- 只保存最佳验证损失的模型
- 定期备份重要的模型文件
- 记录训练配置以便复现

## 🔧 故障排除

### 常见问题

1. **CUDA out of memory**
   - 降低batch_size
   - 减少num_workers
   - 使用CPU训练

2. **GEE连接失败**
   - 检查网络连接
   - 重新认证GEE
   - 确认项目ID正确

3. **数据加载失败**
   - 确认前两阶段已完成
   - 检查Asset ID是否正确
   - 验证数据访问权限

4. **训练收敛慢**
   - 调整学习率
   - 增加训练轮数
   - 检查数据质量

### 调试模式
如需调试，可以：
1. 减少数据量进行快速测试
2. 增加日志输出
3. 保存中间结果

## 📈 后续使用

训练完成后，可以：
1. 使用跨尺度转换模型进行影像风格转换
2. 使用ConvLSTM模型进行时间序列预测
3. 评估模型在不同地表类型上的性能
4. 进行模型微调和优化

---

**重要提醒**：
- 建议在DL环境中运行以获得最佳性能
- 首次运行建议使用低配置参数验证流程
- 确保有足够的存储空间保存模型文件
- 定期监控训练进度和系统资源
