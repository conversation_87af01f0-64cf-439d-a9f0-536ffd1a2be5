---

### **第二阶段：物理模型 - 生成“虚拟高分影像” (完整修订版)**

**核心目标：** 将离散的、每日更新的物理参数，通过BRDF物理模型，转化为一个连续的、时间上极其密集（每10-15分钟）、空间上高分辨率（500米）的**理论地表反射率影像序列**。这个序列将作为“理想世界”的参照，是后续所有深度学习的基础。

#### **所需数据 (Inputs for this Phase):**

1.  **MODIS BRDF 模型系数 (`modis_brdf_collection`)**: [来自第一阶段] 每日更新的、500米分辨率的地表物理反光特性参数 ($f_{iso}, f_{vol}, f_{geo}$)。
2.  **GOES-17 影像集 (`goes_collection`)**: [来自第一阶段] 我们仅使用其**时间戳**，来构建我们的“生产计划表”。

---

### **详细理论步骤**

#### **步骤一：建立“生产计划表” (Define the Rendering Schedule)**

*   **目的：** 确保我们生成的每一张“理论影像”都能和一张真实的“观测影像”在时间上精确对应。
*   **方法：**
    1.  从`goes_collection`影像集合中，提取出一个**精确到分钟或秒的时间戳列表**。
    2.  这个列表覆盖了整个研究时段（`2021-07-12`至`2021-07-18`），它就是我们后续所有计算任务的时间基准。我们将为列表中的**每一个时间点**生成一张高分辨率虚拟影像。

#### **步骤二：理解并准备BRDF-t模型的核心公式**

*   **目的：** 这是整个阶段的理论引擎。理解它，就是理解如何从物理参数计算出影像的亮度。
*   **来源：** 论文 **公式(1)**。
*   **公式 (Markdown格式):**
    $$
    R(\theta_s, \theta_v, \phi, \lambda, t) = f_{iso}(\lambda, t) + f_{vol}(\lambda, t)K_{vol}(\theta_s, \theta_v, \phi) + f_{geo}(\lambda, t)K_{geo}(\theta_s, \theta_v, \phi)
    $$
*   **公式参数详解:**
    *   $R(...)$: 我们要求解的**理论反射率**（虚拟影像的像素值）。
    *   $f_{iso}, f_{vol}, f_{geo}$: **地表物理参数**，其数值直接从`modis_brdf_collection`中读取。
    *   $K_{vol}, K_{geo}$: 标准的数学“核函数”，根据输入的几何角度调整$f_{vol}$和$f_{geo}$的权重。
    *   $\theta_s, \theta_v, \phi$: **太阳天顶角**、**观测天顶角**、**相对方位角**。

#### **步骤三：为模型准备具体的、按日更新的数值输入**

*   **目的：** 将公式中的符号替换为真实的、可计算的、且随时间精确变化的数字。
*   **方法：**
    1.  **准备观测几何 ($\theta_v, \phi_v$):**
        *   **静态输入：** GOES-17是地球静止卫星，其观测研究区`aoi`的角度基本**固定不变**。你需要为你的`aoi`计算一次GOES-17的观测天顶角和观测方位角，并在后续所有计算中**重复使用**这些固定的角度值。
    2.  **准备光照几何 ($\theta_s, \phi_s$):**
        *   **动态输入：** 太阳的位置是**持续变化**的。你需要遍历“生产计划表”中的**每一个时间戳**，并为研究区内的**每一个500米像素**计算出该精确时刻的**太阳天顶角**和**太阳方位角**。
    3.  **准备地表参数 ($f_{iso}, f_{vol}, f_{geo}$) - 融合儒略日调整：**
        *   **这是确保模型精度的关键步骤。**
        *   **动态输入（按日更新）：** 地球的季节性变化导致地表特性逐日变化。`modis_brdf_collection`恰好是每日更新的产品，完美地捕捉了这种变化。
        *   **操作：** 你**不能**用一套BRDF参数去计算所有日期的影像。在进入主计算循环时，你必须严格遵循**按儒略日（Julian Day）匹配**的原则。具体操作将在下一步骤中详述。

#### **步骤四：执行“渲染”循环，生成虚拟影像序列 (已整合儒略日调整)**

*   **目的：** 将以上所有准备好的数据和参数组合起来，通过一个严谨的嵌套循环，最终生成我们需要的虚拟影像集。
*   **嵌套循环流程：**

    1.  **外层循环 (按日期/儒略日):** `For` 每一个 `day` `in` 你的研究时段 (`2021-07-12` to `2021-07-18`):
        a.  **【关键：儒略日调整】**
            *   从`modis_brdf_collection`中，使用当前`day`作为过滤器，筛选出**仅属于当天**的那**唯一一张**BRDF参数影像。我们将这张影像称为 `daily_brdf_params`。这一步确保了接下来的所有计算都将使用与地球当天物理状况最匹配的参数。

        b.  **中层循环 (按当日时间戳):** `For` 每一个属于当前`day`的`timestamp` `in` 你的“生产计划表”:
            i.   创建一个新的、空白的、500米分辨率的影像，命名为`virtual_image_at_timestamp`，用于存储当前时刻的计算结果。
            ii.  **内层循环 (按像素):** `For` 每一个`pixel` `in` 你的研究区`aoi`:
                *   **获取地表参数:** 从 `daily_brdf_params`（已为当日专属）中，读取当前`pixel`的 $f_{iso}, f_{vol}, f_{geo}$ 值。
                *   **获取静态几何:** 获取当前`pixel`的固定观测角度 ($\theta_v, \phi_v$)。
                *   **获取动态几何:** 计算当前`pixel`在当前`timestamp`下的太阳角度 ($\theta_s, \phi_s$)。
                *   **执行公式计算:** 将以上所有参数值代入**步骤二的BRDF公式**，计算出最终的理论反射率 $R$。
                *   **填充结果:** 将计算出的 $R$ 值，填充到`virtual_image_at_timestamp`的对应`pixel`位置。
            iii. 当内层循环（像素级循环）结束，`virtual_image_at_timestamp`这张影像就被完全填满了。将这张完整的虚拟影像保存下来，并将其与`timestamp`关联。

    2.  当外层循环（日期循环）结束，你就完成并保存了整个研究时段的所有虚拟影像。

---

**阶段最终产出 (Expected Outcome):**

*   一个**全新的GEE影像集合 (`virtual_image_collection`)**。这个集合中的每一张影像都具有以下属性：
    *   **空间分辨率：** 500米（高分辨率）。
    *   **时间戳：** 与GOES-17的观测时间完全同步（高时间频率）。
    *   **内容：** 完全由物理模型生成的、**每日参数更新的**、无云、无大气干扰、无异常事件的**理论地表反射率**。
