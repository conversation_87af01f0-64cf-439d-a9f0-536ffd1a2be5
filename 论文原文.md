<!-- Meanless: IEEE GEOSCIENCE AND REMOTE SENSING LETTERS, VOL. 20, 2023 6009205-->

# Coupling Physical Model and Deep Learning for Near Real-Time Wildfire Detection

Fengcheng Ji ${}^{ \oplus  }$ , <PERSON><PERSON> Zhao ${}^{ \ominus  }$ , Member, IEEE, <PERSON><PERSON>, <PERSON><PERSON> Chen ${}^{ \ominus  }$ , <PERSON><PERSON> Li, <PERSON><PERSON> ${}^{ \oplus  }$ , and <PERSON><PERSON><PERSON>

Abstract-Accurate and timely monitoring of wildfires is crucial for reducing property damage and casualties. In recent years, advances in satellite technology have enabled the comprehensive, timely, and rapid recording of various abrupt events on the Earth's surface. However, achieving a balance between temporal and spatial resolution remains a significant challenge for remote sensing, hindering the quick and accurate detection of wildfires. This letter proposes a novel framework for the near real-time monitoring of wildfire coupled with the bidirectional reflectance distribution function (BRDF) model and deep learning technology, which enables near real-time detection of wildfire by assessing the degree to which the observed value of geostationary satellite image deviates from the predicted theoretical observation value. The experimental results show that the proposed method is capable of effectively detecting wildfires in near real-time. Moreover, the encouraging results suggest that the method holds promise for monitoring the spread of wildfire to a certain extent.

Index Terms- Bidirectional reflectance distribution function (BRDF), deep learning, remote sensing, wildfire detection.

## I. INTRODUCTION

WILDFIRE is one of the most serious natural disasters in the world causing huge losses to the ecological environment and even human society [1]. Given such an imminent threat, more efficient wildfire monitoring technology, especially early wildfire detection, needs to be developed. As one of the most efficient ways, remote sensing image plays an important role in detecting or monitoring active wildfire through the optical or thermal sensors carried by satellites [2].

Conventionally, change detection methods based on bitemporal or time series were widely utilized for wildfire recognition, especially using high-spatial resolution remote sensing imagery that can provide richer and finer spatial and spectral information [3], [4]. However, both bi-temporal and time-series change detection methods require up-to-date remote sensing data, which is highly restricted by the fixed revisit period of low Earth orbit (LEO) satellites.

Complementary, geostationary satellites with higher orbit altitudes can provide dense revisit frequency in a near real-time manner, such as GOES-17, FengYun-4, and Himawari-8, etc., thus providing opportunities to monitor wildfire in the early stage [5], [6], [7]. Meanwhile, the free and open access to the whole geostationary satellites has also further promoted its use in wildfire monitoring. However, the spatial resolution dramatically decreased due to the orbit altitude of geostationary satellites, usually with a few kilometers per pixel, which brings the problem of pixel mixing that prevent applicability from wildfire detection. For example, it is hard to interpret the source of the spectrum signal whether from internal variations (e.g., seasonal or weather variation) or external changes (e.g., wildfire). Additionally, highly mixed pixel and lower resolution make it hard to maintain stability and explore the patterns of change under an ideal state.

To make the geostationary satellite image more interpretable and better explore the change patterns, a feasible way is to conduct spectrum unmixing that decomposes the mixed pixel into stable pure endmembers. Instead of selecting endmem-bers from low-resolution geostationary images themselves, the polar orbit satellites such as moderate-resolution imaging spectroradiometer (MODIS), Landsat, and Sentinel can provide high-resolution information at a finer scale that could be used for resolution improvement by establishing the mapping relationship between the low-/high-resolution remote sensing images [8], [9]. Unfortunately, limited to the revisit frequency of polar satellites, it is difficult to acquire dense time series to match with geostationary remote sensing imagery. As a possible solution, physical models, such as radiative transfer models (i.e., RTs) can be utilized to create densely distributed high-resolution images to pair with geostationary ones. Among them, a commonly used RTs is the bidirectional reflectance distribution function (BRDF) proposed by [10], which has been widely used in land cover (LC) classification, cloud detection, deriving albedo, and so on [11], [12], [13]. Still, the mainstream BRDF models are usually constructed based on historical observations (e.g., the latest MODIS BRDF model was built on the last 16-day time series), and under the assumption of reflectance hysteretic, it only supports the historical data refinement and regeneration. In this way, the BRDF-derived virtual images are capable to perform post-detection (after the wildfire event) while leaving the challenge of real-time detection unsolved.

To realize near real-time wildfire detection, it is necessary to provide theoretical data ahead of geostationary satellite observations. Recently, with the development of technology, data-driven models such as convolution neural networks (CNNs), generative adversarial networks (GANs), and recurrent neural networks (RNNs) achieve outstanding performance in data mining [14], [15]. Especially, RNNs and their variants have been widely implemented in the prediction of sequence data, by which the theoretical data can be predicted given a period of stable time-series observations [16], [17]. Hence, the integration of predicted theoretical data and geostationary satellite observations makes the near real-time monitoring of wildfires possible.

---

<!-- Footnote -->

Manuscript received 6 May 2023; revised 28 July 2023; accepted 17 August 2023. Date of publication 21 August 2023; date of current version 1 September 2023. This work was supported in part by the National Natural Science Foundation of China Major Program under Grant 42192580 and Grant 42192584 and in part by the National Natural Science Foundation of China under Grant 62201063. (Corresponding authors: Wenzhi Zhao; Jiage Chen.)

Fengcheng Ji, Wenzhi Zhao, Qiao Wang, Kaiyuan Li, and Rui Peng are with the State Key Laboratory of Remote Sensing Science, and the Beijing Engineering Research Center for Global Land Remote Sensing Products, Institute of Remote Sensing Science and Engineering, Faculty of Geographical Science, Beijing Normal University, Beijing 100875, China (e-mail: <EMAIL>; <EMAIL>; <EMAIL>; <EMAIL>; <EMAIL>).

Jiage Chen is with the National Geomatics Center of China, Beijing 100830, China (e-mail: <EMAIL>).

Jichao Wu is with the Alibaba DAMO Academy, Hangzhou 311121, China (e-mail: <EMAIL>).

Digital Object Identifier 10.1109/LGRS.2023.3307129

<!-- Footnote -->

---

<!-- Meanless: 1558-0571 © 2023 IEEE. Personal use is permitted, but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information. Authorized licensed use limited to: Jiangsu Ocean University. Downloaded on June 06,2025 at 00:31:53 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: 6009205 IEEE GEOSCIENCE AND REMOTE SENSING LETTERS, VOL. 20, 2023-->

<!-- Media -->

<!-- figureText: Geostationary Satellites Polar EO Satellites Step3. Future prediction Densified high-resolution images Geo image 1 Geostationary satellite Time series LC Sequences (C) Mapping High-resolution High-resolution Predicted Time series Sequence Step1. BRDF-enabled dense time-series generation Solar angle View angle BRDF-t model Iso,Vol,Gec Image Generation Julian day Parameter setting Model optimization Step2. Cross-Scale Conversion Geo image 1 Geo image 2 Landcover LC1 LC 2 LC 3 LC 17 High-resolution High-resolution image 1 Predicted Detected Wildfires -->

<img src="https://cdn.noedgeai.com/bo_d20dirv7aajc73bfn9tg_1.jpg?x=167&y=146&w=683&h=506&r=0"/>

Fig. 1. Overall framework of the wildfire detection.

<!-- Media -->

Based on the above principle, this letter focuses on the theoretical data prediction of geostationary satellites by combining the advantages of physical and deep learning models. We achieved the near real-time detection of wildfires through the calculation of the degree to which the observed data deviates from the predicted theoretical data.

## II. METHODOLOGY

The technical route of this letter is shown in Fig. 1, which mainly includes BRDF-enabled dense time-series generation, cross-scale conversion, stable condition prediction, and wildfire detection and trace. In the first stage, a dense time series of high spatial resolution images was generated with the help of the BRDF-t model. In the second stage, we established the mapping relationship between generated data and geostationary satellite image through cross-scale conversion, and further, the theoretical observation value of the geostationary satellite was predicted by combining it with the improved long short term memory (LSTM). In the third stage, we compared the degree to which the observed value of geostationary satellite image deviates from the theoretical observation value and obtained the results of wildfire detection.

## A. BRDF-Enabled Dense Time-Series Generation

To generate dense time series, the linear kernel-driven BRDF model was expanded in terms of temporal dimension (i.e., RRDF-t) while utilized to densify the time series of high spatial resolution data. The equation of the model can be expressed as

$$
R\left( {\varphi ,\theta ,\phi ,\lambda ,t}\right)  = {f}_{\text{iso }}\left( \lambda \right)  + {f}_{\text{vol }}\left( \lambda \right) {K}_{\text{vol }}\left( {\varphi ,\theta ,\phi ,t}\right) 
$$

$$
 + {f}_{\text{geo }}\left( \lambda \right) {K}_{\text{geo }}\left( {\varphi ,\theta ,\phi ,t}\right)  \tag{1}
$$

where $R$ is the generated data in wavelength $\lambda .{K}_{\text{vol }}$ and ${K}_{\text{geo }}$ denote the volumetric scattering kernel and geometric optical model, respectively, which are the functions of solar zenith angle $\theta$ ,view zenith angle $\varphi$ ,and relative azimuth angle $\phi$ . $\left( {{f}_{\text{iso }},{f}_{\text{vol }}}\right.$ ,and $\left. {f}_{\text{geo }}\right)$ are the weight coefficients that represent the weight of these three kernels. And $t$ refers to the moment that needs to generate data, which corresponds to the geostationary satellite. To minimizes undesirable variations from angle difference in the cross-scale conversion process, the view angle was set the same as the geostationary satellite.

## B. Cross-Scale Conversion and Stable Condition Prediction

In order to explore the changing pattern of geostationary satellite data under the ideal state and further facilitate the prediction of theoretical observation value, cross-scale conversion with linear unmixing modular was designed to establish the mapping relationship between the geostationary satellite and generated data. Specifically, we assumed that the mixed pixel $y$ of a geostationary satellite is a linear combination of generated pixels ${L}_{i}$ (pure endmembers) according to the LC type, and the spectral value of which can be solved by the weighted average of the pure endmembers. The linear model can be expressed as follows:

$$
y = \mathop{\sum }\limits_{{i = 1}}^{n}{A}_{i}{L}_{i} + \omega  \tag{2}
$$

where $y$ is the spectral value of the mixed pixel. $n$ refers to the number of LC types. ${A}_{i}$ denotes the weight coefficient of the $i$ th pure endmember. ${L}_{i}$ represents the spectral value of $i$ th pure endmember. $\omega$ is the residual. In this way,the generated dense high-resolution imagery could be converted into geostationary satellite data with the help of cross-scale conversion model.

However, it is impossible to perform real-time wildfire detection without any given theoretical observation value ahead. To facilitate the real-time wildfire screening, the prediction model based on LSTM was utilized to model the recursive prediction process of the theoretical value based on the historical time series, and the prediction process can be described as

$$
\widehat{y} = A \cdot  \left( {\mathcal{L} \otimes  L}\right)  + \omega  \tag{3}
$$

where $\widehat{y}$ is the predicted theoretical value, $\mathcal{L}$ represents the prediction model (Fig. 2),and $\otimes$ denotes the recursive prediction operation. As shown in Fig. 2, the generated sequence will continuously pass through the LSTM cells. Within each cell unit, the flow of information is controlled using forget gates ${f}_{t}$ ,input gates ${i}_{t}$ ,and output gates ${o}_{t}$ ,while the cell state ${C}_{t}$ is used to capture and store temporal features and hidden state ${h}_{t}$ is the output state of the model at each time step. The cell state ${C}_{t}$ and hidden state ${h}_{t}$ can be represented by the following equation:

$$
{C}_{t} = {f}_{t} * {C}_{t - 1} + {i}_{t} * {\widetilde{C}}_{t} \tag{4}
$$

$$
{h}_{t} = {o}_{t} * \tan h\left( {C}_{t}\right)  \tag{5}
$$

where ${\widetilde{C}}_{t}$ is candidate state. To make the prediction model more robust to the input noise and prevent losing historical information, we concatenated one-step and recursive prediction of the LSTM model for the prediction model (Fig. 2), and let the loss consist of three parts in the training stage: free running loss, teacher forcing loss, and simplified professor forcing loss. And the mean squared error was chosen as the loss function

<!-- Meanless: Authorized licensed use limited to: Jiangsu Ocean University. Downloaded on June 06,2025 at 00:31:53 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: JI et al.: COUPLING PHYSICAL MODEL AND DEEP LEARNING FOR NEAR REAL-TIME WILDFIRE DETECTION 6009205-->

$$
\text{ Loss } = \frac{1}{n}\mathop{\sum }\limits_{{i = 1}}^{n}\left( {{\left( {y}_{i} - {\widehat{y}}_{fi}\right) }^{2} + {\left( {y}_{i} - {\widehat{y}}_{ti}\right) }^{2} + {\left( {h}_{i} - {\widehat{h}}_{i}\right) }^{2}}\right)  \tag{6}
$$

<!-- Media -->

<!-- figureText: $\left( {{c}_{1},{h}_{1}}\right)$ :LSTM Cell :ground truth :hidden state $\left( {{c}_{2},{h}_{2}}\right)$ $\left( {{c}_{3},{h}_{3}}\right)$ $\left( {{c}_{4},{h}_{4}}\right)$ $\left( {{c}_{s},{h}_{s}}\right)$ :cell state : 1-step prediction :recursive prediction Geo Satellite Polar Satellite (fixed) Generated sequence BRDF-t -->

<img src="https://cdn.noedgeai.com/bo_d20dirv7aajc73bfn9tg_2.jpg?x=190&y=147&w=1432&h=497&r=0"/>

Fig. 2. Architecture of the recursive prediction model.

<!-- figureText: ${130}^{ \circ  }{0}^{\prime }{0}^{\prime \prime }\mathrm{W}$ ${120}^{ \circ  }{0}^{\prime }{0}^{\prime \prime }\mathrm{W}$ ${110}^{ \circ  }{0}^{\prime }{0}^{\prime \prime }\mathrm{W}$ 300 ${40}^{ \circ  }{0}^{\prime }{0}^{\prime \prime }\mathrm{N}\;{45}^{ \circ  }{0}^{\prime }{0}^{\prime \prime }\mathrm{N}\;{50}^{ \circ  }{0}^{\prime }{0}^{\prime \prime }$ -->

<img src="https://cdn.noedgeai.com/bo_d20dirv7aajc73bfn9tg_2.jpg?x=152&y=704&w=716&h=298&r=0"/>

Fig. 3. Study area.

<!-- Media -->

where $n$ is the sequence length, ${y}_{i}$ denotes the true value. ${\widehat{y}}_{fi}$ and ${\widehat{y}}_{ti}$ represent the output of one-step and multistep prediction,respectively. ${h}_{i}$ is the hidden states from one-step prediction,and ${\widehat{h}}_{i}$ denotes hidden states from multistep prediction. In the prediction stage, the final result of the one-step prediction is used as the input of recursive prediction. Based on the above methods, we can obtain the stable theoretical observation values of geostationary satellites, and further, the wildfire can be detected in real-time when the deviation between observed values and theoretical values exceeds the threshold of 1500.

## III. EXPERIMENT AND RESULTS

## A. Study Area and Data

To verify the feasibility of the proposed method, this letter used the wildfire accidents in the Okanagan-Similkameen C region of British Columbia, Canada, on July 19, 2021, to experiment, as shown in Fig. 3. In the experiment, there are mainly three types of data involved, namely kernel coefficients of the BRDF model, LC data (MODIS product: MODIS LC Type Yearly Global ${500}\mathrm{\;m}$ ),and geostationary satellite images, which are used to generate data and establish mapping relationship, respectively. All the above data are available on the GEE platform, and the information summary of these data is shown in Table I.

## B. Data Preparation

The reflection and scattering of solar light by objects will change as the Earth rotates, to which the BRDF-t model is sensitive. Thus, kernel coefficients must be adjusted to the Julian day before being input into the BRDF-t model. To establish the robust mapping relationship, we selected 12000 cloud-free sample sequences with lengths of 1008 (7 days $\times  {144}$ per day) from geostationary satellite images for the training of the cross-scale conversion model. Correspondingly, the generated data sequences corresponding to the above samples are prepared. Similarly, we respectively selected 3500 representative sample sequences with a length of 1008 for each LC type from the generated data to train the prediction model. It is worth noting that the sample proportion for both training and validation in the experiment was all set to 7:3, and a cloud-free sample in this study is defined as a pixel where the proportion of cloud-free pixels within ${24}\mathrm{\;h}$ accounts for more than ${85}\%$ of the observed pixel,where the cloud-free pixel can be obtained as follows:

$$
\left( {{B}_{2} - {B}_{1}}\right) /\left( {{B}_{2} + {B}_{1}}\right)  > \varrho  \tag{7}
$$

where ${B}_{1}$ and ${B}_{2}$ are the first and second bands of the geostationary satellite,respectively. And $\varrho$ is the threshold.

## C. Experiment Configuration

For the prediction model, the number of layers is set to 2 . The number of hidden units per layer is 32 . And the optimizer used in the model is Adam. The learning rate and batch_size are set to 0.0002 and 64, respectively. The training epoch is set to 400 , and the training process was completed on a device consisting of 11th Gen Intel ${}^{1}$ Core ${}^{2}$ i9-11900K,RAM (128 GB), and GPU (Nvidia GeForce RTX 3060 with 12 GB). Besides,a final predicted length of ${12}\mathrm{\;h}$ is set. For data generation, all the processes are completed with the help of cloud computing technology provided by Alibaba, and optimization schemes (e.g., data partitioning and distributed computing) are also utilized to speed up the process.

## D. Data Generation and Prediction

Fig. 4. qualitatively shows the generated data and predicted results of the study area along the temporal axis. As can be seen from Fig. 4(a)-(d), the generated data can excellently simulate the real situation (such as object distribution and adjacency relationship) of the ground, which is mainly due to the utilized BRDF-t model being capable of quantitatively describing the reflectance according to the electromagnetic spectrum and radiation transmission characteristic well. It is worth noting that the generated data can effectively make up for the lack of temporal resolution of polar satellite images. Additionally, the predicted results of generated data shown in Fig. 4(e)-(f) keep highly consistent with the Earth's surface with details preserved, which verifies the capability of the proposed prediction model in recursive prediction. The predicted theoretical observation data of geostationary satellites were obtained by conducting cross-scale conversion on the predicted results of generated data, and the results are shown in Fig. 4(i)-(l), which also preserves the details of the Earth's surface. The successful prediction of the theoretical observation value of the geostationary satellite also establishes the foundation for the near real-time detection of wildfires.

---

<!-- Footnote -->

${}^{1}$ Registered trademark.

${}^{2}$ Trademarked.

<!-- Footnote -->

---

<!-- Meanless: Authorized licensed use limited to: Jiangsu Ocean University. Downloaded on June 06,2025 at 00:31:53 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: 6009205 IEEE GEOSCIENCE AND REMOTE SENSING LETTERS, VOL. 20, 2023-->

<!-- Media -->

TABLE I

INFORMATION SUMMARY

<table><tr><td>Information Type</td><td>GOES-17</td><td>Kernel coefficient of the BRDF</td><td>Land Cover</td></tr><tr><td>Time Range</td><td>2021.07.12 ~ 2021.07.18</td><td>2021.07.12 ~ 2021.07.18</td><td>2021.07.12 ~ 2021.07.18</td></tr><tr><td>Spatial Range</td><td/><td colspan="2">${45.24}^{ \circ  }\mathrm{N} \sim  {49.77}^{ \circ  }\mathrm{N},{118.27}^{ \circ  }\mathrm{W} \sim  {124.20}^{ \circ  }\mathrm{W}$</td></tr><tr><td>Resolution</td><td>$2\mathrm{\;{km}}$</td><td>0.5 km</td><td>0.5km</td></tr><tr><td>Band</td><td>All bands</td><td>All bands</td><td>All bands</td></tr><tr><td>Data Level</td><td>L2</td><td>/</td><td>/</td></tr></table>

<!-- figureText: (a) (b) (c) (d) (g) (h) (k) (o) (p) (e) (f) (i) (g) (m) (n) -->

<img src="https://cdn.noedgeai.com/bo_d20dirv7aajc73bfn9tg_3.jpg?x=155&y=470&w=718&h=561&r=0"/>

Fig. 4. Results of the generated data and predicted data. (a)-(d) Generated data along the temporal axis. (e)-(h) Predicted results of generated data along the temporal axis. (i)-(l) Predicted results of geostationary satellites. (m)-(p) Observed data of geostationary satellite along the temporal axis. From left to right, the time is 13:30, 14:00, 15:00, and 16:00.

<!-- Media -->

To quantitatively analyze the predicted results, the RMSE and ${R}^{2}$ are used as the accuracy evaluation indicators to calculate the consistency between the predicted value and the observed value. The calculation formulas are as follows:

$$
\operatorname{RMSE} = \frac{\mathop{\sum }\limits_{{i = 1}}^{N}\parallel y\left( i\right)  - \widehat{y}\left( i\right) \parallel }{\sqrt{N}} \tag{8}
$$

$$
{R}^{2} = 1 - \frac{\mathop{\sum }\limits_{{i = 1}}^{N}{\left( y\left( i\right)  - \widehat{y}\left( i\right) \right) }^{2}}{\mathop{\sum }\limits_{{i = 1}}^{N}{\left( y\left( i\right)  - \bar{y}\left( i\right) \right) }^{2}} \tag{9}
$$

where $N$ is the number of observation samples and $i$ is the series number of the samples. $\widehat{y},\bar{y}$ ,and $y$ refer to the predicted value,mean value,and observed value at location $i$ , respectively.

<!-- Media -->

<!-- figureText: (a) ${RMSE}$ (b) -->

<img src="https://cdn.noedgeai.com/bo_d20dirv7aajc73bfn9tg_3.jpg?x=929&y=471&w=716&h=292&r=0"/>

Fig. 5. Spatial distribution of estimation error with cloud masked. (a) Spatial distribution of ${R}^{2}$ . (b) Spatial distribution of RMSE.

<!-- figureText: ① 13:50 14:00 ① (C) 14:40 14:50 15:30 15:40 16:20 16:30 13:20 13:30 13:40 14:10 14:20 14:30 15:00 15:10 15:20 15:50 16:00 16:10 -->

<img src="https://cdn.noedgeai.com/bo_d20dirv7aajc73bfn9tg_3.jpg?x=926&y=826&w=720&h=464&r=0"/>

Fig. 6. Wildfire detection results along the temporal axis from 13:20 to 16:30, where 13:20-15:00 and 15:00-16:30 are the rapid spread stage and severe stage of the first wildfire, respectively, 15:40-16:10 and 16:10-16:30 are the initial stage and rapid spread stage of the second wildfire, respectively.

<!-- Media -->

Fig. 5 shows the spatial distribution of estimation errors of each pixel with cloud masked along the temporal axis. As can be seen from Fig. 5,the overall ${R}^{2}$ is greater than 0.6 but exists a few low values. Similarly, the overall RMSE is lower than 0.10 but has few high values. In general, it is still evident that the predicted results are largely in line with the observed data. Furthermore,the spatial distribution of the ${R}^{2}$ and RMSE values are consistent, which also verifies the effectiveness of the proposed model.

## E. Wildfire Detection and Trace

Figs. 6 and 7 show the results of wildfire detection and wildfire spread process by comparing the observed value with the predicted theoretical observation value of geostationary satellite along the temporal axis. As can be seen from Fig. 6, a total of two wildfires were detected. The detailed information of the detected wildfires is shown in Table II.

For the first detected wildfire, although it had already occurred when we detected it, the spread process can still be clearly monitored in real-time, which is mainly due to the fact that the theoretical value had already been predicted ahead and was compared with the updated observed value. This can enable the tracking of wildfire development over time, the prediction of its spread trend, and the development of appropriate plans for wildfire response and rescue efforts. For the second wildfire,it was detected at 3:40 P.M. through the proposed model. By comparing it with the GOES fire product, we found that the detected wildfire has only a 20-min delay from the real wildfire, which realizes the near real-time detection for abrupt wildfires and proves the effectiveness of our proposed method. In theory, the gap between wildfire occurrence and detection can be further reduced as the shortening of satellite revisit cycles and the improvement of predictive model performance.

<!-- Meanless: Authorized licensed use limited to: Jiangsu Ocean University. Downloaded on June 06,2025 at 00:31:53 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: JI et al.: COUPLING PHYSICAL MODEL AND DEEP LEARNING FOR NEAR REAL-TIME WILDFIRE DETECTION 6009205-->

<!-- Media -->

<!-- figureText: wildfire 13:20 ${13} : {30}$ 13:40 14:10 14:20 14:50 15:00 13:50 14:00 14:30 14:40 -->

<img src="https://cdn.noedgeai.com/bo_d20dirv7aajc73bfn9tg_4.jpg?x=154&y=148&w=714&h=507&r=0"/>

Fig. 7. Monitoring for wildfire spread process.

TABLE II

WILDFIRE DETECTION RESULTS

<table><tr><td>ID</td><td>Coordinate</td><td>Start</td><td>Observed</td><td>Time-lag</td></tr><tr><td>1</td><td>(48.66°N, 120.08°W)</td><td>/</td><td>13:20</td><td>/</td></tr><tr><td>2</td><td>(49.11°N, 119.48°W)</td><td>15:20</td><td>15:40</td><td>20</td></tr></table>

<!-- Media -->

Compared with traditional methods, such as threshold-based and change detection-based, the strategy proposed in this letter offers two main advantages:

1) Instead of relying on a fixed threshold obtained through historical data statistics, this method detects wildfires by calculating the degree of deviation between the observed value and the theoretical value at the corresponding time, which mitigates the influence caused by observational errors resulting from diurnal change, weather patterns or seasonal fluctuations on detection results.

2) The method presented in this letter eliminates the need to extract features from the observed data and instead directly assesses the observed data itself, resulting in reduced processing time for wildfire detection.

## IV. Conclusion

In this letter, a near real-time wildfire detection framework based on multisource data collaboration is presented. Specifically, to achieve the purpose of near real-time wildfire detection, we integrated the advantages of the physical model and deep learning technology to combine the geostationary satellite imagery with polar ones, and successfully predicted the theoretical observation value of the geostationary satellite ahead. Thus, providing minute-level benchmark data for wildfire monitoring when new observation data are acquired. The experimental result of data generation and prediction in the study area demonstrates that the proposed framework can accurately predict theoretical data and achieve high consistency with the ground truth. Furthermore, the experimental result of wildfire detection verifies that the proposed framework has the potential to enable minute-level wildfire detection and trace the spread of wildfires. In general, the integration of physical models and deep learning technology can effectively address the data fusion challenge, enabling an opportunity for near real-time wildfire detection.

In future investigations, we aim to expand the model to additional spectral bands, such as microwave and infrared, etc., to achieve all-weather wildfire detection. REFERENCES

[1] A. Shama et al., "A burned area extracting method using polarization and texture feature of Sentinel-1A images," IEEE Geosci. Remote Sens. Lett., vol. 20, 2023, Art. no. 2501305.

[2] K. Thangavel, D. Spiller, R. Sabatini, P. Marzocca, and M. Esposito, "Near real-time wildfire management using distributed satellite system," IEEE Geosci. Remote Sens. Lett., vol. 20, 2023, Art. no. 5500705.

[3] D. Rashkovetsky, F. Mauracher, M. Langer, and M. Schmitt, "Wildfire detection from multisensor satellite imagery using deep semantic segmentation," IEEE J. Sel. Topics Appl. Earth Observ. Remote Sens., vol. 14, pp. 7001-7016, 2021.

[4] L. Ying, Z. Shen, M. Yang, and S. Piao, "Wildfire detection probability of MODIS fire products under the constraint of environmental factors: A study based on confirmed ground wildfire records," Remote Sens., vol. 11, no. 24, p. 3031, Dec. 2019.

[5] B. Hally, L. Wallace, K. Reinke, S. Jones, and A. Skidmore, "Advances in active fire detection using a multi-temporal method for next-generation geostationary satellite data," Int. J. Digit. Earth, vol. 12, no. 9, pp. 1030-1045, Sep. 2019.

[6] S. Li et al., "First provisional land surface reflectance product from geostationary satellite Himawari-8 AHI," Remote Sens., vol. 11, no. 24, p. 2990, Dec. 2019.

[7] Z. Zhang and Q. Du, "Hourly mapping of surface air temperature by blending geostationary datasets from the two-satellite system of GOES-R series," ISPRS J. Photogramm. Remote Sens., vol. 183, pp. 111-128, Jan. 2022.

[8] S. Mei, R. Jiang, X. Li, and Q. Du, "Spatial and spectral joint super-resolution using convolutional neural network," IEEE Trans. Geosci. Remote Sens., vol. 58, no. 7, pp. 4590-4603, Jul. 2020.

[9] X. Dou, C. Li, Q. Shi, and M. Liu, "Super-resolution for hyperspectral remote sensing images based on the 3D attention-SRGAN network," Remote Sens., vol. 12, no. 7, p. 1204, Apr. 2020.

[10] J.-L. Roujean, M. Leroy, and P.-Y. Deschamps, "A bidirectional reflectance model of the Earth's surface for the correction of remote sensing data," J. Geophys. Res., Atmos., vol. 97, no. D18, pp. 20455-20468, Dec. 1992.

[11] Z. Jiao et al., "Development of a snow kernel to better model the anisotropic reflectance of pure snow in a kernel-driven BRDF model framework," Remote Sens. Environ., vol. 221, pp. 198-209, Feb. 2019.

[12] Y. Guan, Y. Zhou, B. He, X. Liu, H. Zhang, and S. Feng, "Improving land cover change detection and classification with BRDF correction and spatial feature extraction using Landsat time series: A case of urbanization in Tianjin, China," IEEE J. Sel. Topics Appl. Earth Observ. Remote Sens., vol. 13, pp. 4166-4177, 2020.

[13] W. C. Snyder and Z. Wan, "BRDF models to predict spectral reflectance and emissivity in the thermal infrared," IEEE Trans. Geosci. Remote Sens., vol. 36, no. 1, pp. 214-225, Jan. 1998.

[14] L. Zhang and L. Zhang, "Artificial intelligence for remote sensing data analysis: A review of challenges and opportunities," IEEE Geosci. Remote Sens. Mag., vol. 10, no. 2, pp. 270-294, Jun. 2022.

[15] L. Zhang, M. Lan, J. Zhang, and D. Tao, "Stagewise unsupervised domain adaptation with adversarial self-training for road segmentation of remote-sensing images," IEEE Trans. Geosci. Remote Sens., vol. 60, 2022, Art. no. 5609413.

[16] D. Salinas, V. Flunkert, J. Gasthaus, and T. Januschowski, "DeepAR: Probabilistic forecasting with autoregressive recurrent networks," Int. J. Forecasting, vol. 36, no. 3, pp. 1181-1191, Jul./Sep. 2020.

[17] C. Xiao, N. Chen, C. Hu, K. Wang, J. Gong, and Z. Chen, "Short and mid-term sea surface temperature prediction using time-series satellite data and LSTM-AdaBoost combination approach," Remote Sens. Environ., vol. 233, Nov. 2019, Art. no. 111358.

<!-- Meanless: Authorized licensed use limited to: Jiangsu Ocean University. Downloaded on June 06,2025 at 00:31:53 UTC from IEEE Xplore. Restrictions apply.-->